import React, { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Send,
  Sparkles,
  BarChart3,
  PieChart,
  LineChart,
  Circle,
  TrendingUp,
  Target,
  Lightbulb,
  MessageSquare
} from 'lucide-react';
import { toast } from 'sonner';
import { ChartType } from '@antv/gpt-vis';
import { GPTVisChart } from './GPTVisChart';
import { ChartRecommendationService, type ChartRecommendation } from '../services/chart-recommendation.service';
import { useDataVisualizationStore } from '../stores/data-visualization.store';

interface QuerySuggestion {
  text: string;
  icon: React.ReactNode;
  category: 'trend' | 'comparison' | 'distribution' | 'correlation' | 'composition';
}

interface QueryResult {
  id: string;
  query: string;
  timestamp: Date;
  recommendations: ChartRecommendation[];
  selectedChart?: ChartRecommendation;
}

const QUERY_SUGGESTIONS: QuerySuggestion[] = [
  {
    text: "Show me trends over time",
    icon: <TrendingUp className="h-4 w-4" />,
    category: 'trend'
  },
  {
    text: "Compare values across categories",
    icon: <BarChart3 className="h-4 w-4" />,
    category: 'comparison'
  },
  {
    text: "What's the distribution of values?",
    icon: <Circle className="h-4 w-4" />,
    category: 'distribution'
  },
  {
    text: "Show proportions and percentages",
    icon: <PieChart className="h-4 w-4" />,
    category: 'composition'
  },
  {
    text: "Find correlations between variables",
    icon: <Target className="h-4 w-4" />,
    category: 'correlation'
  },
  {
    text: "Visualize the data patterns",
    icon: <LineChart className="h-4 w-4" />,
    category: 'trend'
  }
];

interface EnhancedNLQueryInterfaceProps {
  data: any[];
  onVisualizationGenerated?: (chart: ChartRecommendation) => void;
  className?: string;
}

const EnhancedNLQueryInterface: React.FC<EnhancedNLQueryInterfaceProps> = ({
  data,
  onVisualizationGenerated,
  className = ''
}) => {
  const [query, setQuery] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [queryResults, setQueryResults] = useState<QueryResult[]>([]);
  const [selectedResult, setSelectedResult] = useState<QueryResult | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const { addQuery, addQueryResponse } = useDataVisualizationStore();

  // Auto-focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Handle query submission
  const handleSubmitQuery = useCallback(async () => {
    if (!query.trim() || !data || data.length === 0) {
      toast.error('Please enter a query and ensure data is loaded');
      return;
    }

    setIsProcessing(true);
    
    try {
      // Get chart recommendations based on query
      const recommendations = ChartRecommendationService.getRecommendationsForQuery(data, query);
      
      if (recommendations.length === 0) {
        // Fallback to general recommendations
        const analysis = ChartRecommendationService.analyzeData(data);
        const fallbackRecommendations = ChartRecommendationService.getRecommendations(analysis);
        
        if (fallbackRecommendations.length > 0) {
          recommendations.push(...fallbackRecommendations.slice(0, 3));
        }
      }

      const result: QueryResult = {
        id: Date.now().toString(),
        query: query.trim(),
        timestamp: new Date(),
        recommendations,
        selectedChart: recommendations[0] || null
      };

      setQueryResults(prev => [result, ...prev]);
      setSelectedResult(result);
      
      // Store in global state
      addQuery({
        id: result.id,
        query: result.query,
        timestamp: result.timestamp,
        datasetId: 'current', // TODO: Use actual dataset ID
        status: 'completed'
      });

      if (result.selectedChart && onVisualizationGenerated) {
        onVisualizationGenerated(result.selectedChart);
      }

      setQuery('');
      toast.success(`Found ${recommendations.length} visualization suggestions`);
      
    } catch (error) {
      console.error('Error processing query:', error);
      toast.error('Failed to process your query. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  }, [query, data, addQuery, onVisualizationGenerated]);

  // Handle suggestion click
  const handleSuggestionClick = useCallback((suggestion: QuerySuggestion) => {
    setQuery(suggestion.text);
    inputRef.current?.focus();
  }, []);

  // Handle chart selection from recommendations
  const handleChartSelection = useCallback((result: QueryResult, chart: ChartRecommendation) => {
    const updatedResult = { ...result, selectedChart: chart };
    setQueryResults(prev => 
      prev.map(r => r.id === result.id ? updatedResult : r)
    );
    setSelectedResult(updatedResult);
    
    if (onVisualizationGenerated) {
      onVisualizationGenerated(chart);
    }
  }, [onVisualizationGenerated]);

  // Handle Enter key press
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmitQuery();
    }
  }, [handleSubmitQuery]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Query Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Ask About Your Data
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask a question about your data... (e.g., 'Show me sales trends over time')"
              className="flex-1"
              disabled={isProcessing}
            />
            <Button 
              onClick={handleSubmitQuery}
              disabled={isProcessing || !query.trim()}
              className="px-6"
            >
              {isProcessing ? (
                <Sparkles className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* Query Suggestions */}
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground flex items-center gap-2">
              <Lightbulb className="h-4 w-4" />
              Try these suggestions:
            </p>
            <div className="flex flex-wrap gap-2">
              {QUERY_SUGGESTIONS.map((suggestion, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="cursor-pointer hover:bg-accent transition-colors"
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  {suggestion.icon}
                  <span className="ml-1">{suggestion.text}</span>
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Query Results */}
      {queryResults.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Results List */}
          <Card>
            <CardHeader>
              <CardTitle>Query History</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px]">
                <div className="space-y-3">
                  {queryResults.map((result, index) => (
                    <div key={result.id}>
                      <div 
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          selectedResult?.id === result.id 
                            ? 'bg-accent border-primary' 
                            : 'hover:bg-accent/50'
                        }`}
                        onClick={() => setSelectedResult(result)}
                      >
                        <p className="font-medium text-sm">{result.query}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {result.timestamp.toLocaleTimeString()} • {result.recommendations.length} suggestions
                        </p>
                      </div>
                      {index < queryResults.length - 1 && <Separator className="my-2" />}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Selected Result Details */}
          {selectedResult && (
            <Card>
              <CardHeader>
                <CardTitle>Visualization Suggestions</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <div className="space-y-3">
                    {selectedResult.recommendations.map((chart, index) => (
                      <div 
                        key={index}
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          selectedResult.selectedChart === chart 
                            ? 'bg-primary/10 border-primary' 
                            : 'hover:bg-accent/50'
                        }`}
                        onClick={() => handleChartSelection(selectedResult, chart)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-sm">{chart.title}</h4>
                            <p className="text-xs text-muted-foreground mt-1">{chart.description}</p>
                            <p className="text-xs text-muted-foreground mt-2">{chart.reasoning}</p>
                          </div>
                          <Badge variant="secondary" className="ml-2">
                            {Math.round(chart.confidence * 100)}%
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Selected Visualization */}
      {selectedResult?.selectedChart && (
        <GPTVisChart
          chartType={selectedResult.selectedChart.chartType}
          data={data}
          config={selectedResult.selectedChart.config}
          title={selectedResult.selectedChart.title}
          description={selectedResult.selectedChart.description}
        />
      )}
    </div>
  );
};

export default EnhancedNLQueryInterface;
